/*
 * Версия без файловой системы для тестирования
 * Переименуйте в main.cpp если проблемы с LittleFS продолжаются
 */

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>

WebServer server(80);

void setup() {
    delay(3000);
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("\n=== ESP32 BookNook (No FS) ===");
    Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("Chip model: %s\n", ESP.getChipModel());
    
    Serial.println("Step 1: Skipping filesystem (disabled)...");
    
    Serial.println("Step 2: WiFi AP...");
    WiFi.softAP("BookNook-NoFS", "12345678");
    delay(2000);
    Serial.println("WiFi AP started");
    
    Serial.println("Step 3: Web server...");
    server.on("/", []() {
        String html = "<html><body>";
        html += "<h1>BookNook Controller (No FS)</h1>";
        html += "<p>Status: Running without filesystem</p>";
        html += "<p>Free heap: " + String(ESP.getFreeHeap()) + " bytes</p>";
        html += "<p>Uptime: " + String(millis()) + " ms</p>";
        html += "</body></html>";
        server.send(200, "text/html", html);
    });
    
    server.on("/status", []() {
        String json = "{";
        json += "\"heap\":" + String(ESP.getFreeHeap()) + ",";
        json += "\"uptime\":" + String(millis()) + ",";
        json += "\"filesystem\":false";
        json += "}";
        server.send(200, "application/json", json);
    });
    
    server.begin();
    Serial.println("Web server started");
    
    Serial.println("=== SETUP COMPLETE ===");
    Serial.println("Connect to WiFi: BookNook-NoFS");
    Serial.println("Password: 12345678");
    Serial.println("Open browser: http://192.168.4.1");
}

void loop() {
    static unsigned long lastPrint = 0;
    unsigned long now = millis();
    
    // Heartbeat каждые 10 секунд
    if (now - lastPrint >= 10000) {
        Serial.printf("Running... %lu ms, heap: %d bytes\n", now, ESP.getFreeHeap());
        lastPrint = now;
    }
    
    server.handleClient();
    delay(10);
}

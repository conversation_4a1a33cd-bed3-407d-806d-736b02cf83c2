/*
 * Минимальная версия для отладки bootloop
 * Переименуйте в main.cpp если основная версия не работает
 */

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <LittleFS.h>

WebServer server(80);

void setup() {
    delay(5000); // Большая задержка для отладки
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("\n=== MINIMAL ESP32 TEST ===");
    Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("Chip model: %s\n", ESP.getChipModel());
    
    // Минимальная инициализация
    Serial.println("Step 1: LittleFS...");
    if (!LittleFS.begin(true)) {
        Serial.println("LittleFS mount failed, trying format...");
        if (LittleFS.format()) {
            Serial.println("Format OK, retrying mount...");
            LittleFS.begin(false);
        }
    }
    Serial.println("LittleFS OK");
    
    Serial.println("Step 2: WiFi AP...");
    WiFi.softAP("BookNook-Test", "12345678");
    delay(2000);
    Serial.println("WiFi AP OK");
    
    Serial.println("Step 3: Web server...");
    server.on("/", []() {
        server.send(200, "text/plain", "ESP32 is working!");
    });
    server.begin();
    Serial.println("Web server OK");
    
    Serial.println("=== SETUP COMPLETE ===");
    Serial.println("Connect to WiFi: BookNook-Test");
    Serial.println("Open browser: http://***********");
}

void loop() {
    static unsigned long lastPrint = 0;
    unsigned long now = millis();
    
    // Heartbeat каждые 5 секунд
    if (now - lastPrint >= 5000) {
        Serial.printf("Running... %lu ms, heap: %d\n", now, ESP.getFreeHeap());
        lastPrint = now;
    }
    
    server.handleClient();
    delay(10);
}

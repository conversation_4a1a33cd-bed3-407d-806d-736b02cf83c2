<!DOCTYPE html>
<html>
<head>
  <title>Booknook Durin's Doors</title>
  <meta charset="UTF-8">
  <meta name='viewport' content='width=device-width,initial-scale=1'>
  <link href='/uncial-antiqua.css' rel='stylesheet'>
  <link rel="stylesheet" href="/nano.min.css">
  <style>
    @font-face {
      font-family: 'Uncial Antiqua';
      src: url('/UncialAntiqua-Regular.woff2') format('woff2');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
    body { background: #181818; color: #f7f5ed; font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 0; }
    .center { text-align: center; margin-top: 0; }
    .main-card { background: #23201a; max-width: 480px; margin: 32px auto 0 auto; border-radius: 18px; box-shadow: 0 4px 24px rgba(0,0,0,0.18); padding: 0 24px 48px 24px; overflow: hidden; }
    .header-img { width: 100%; height: 160px; background: url('/LoTR-gold.png') center center/contain no-repeat; border-radius: 18px 18px 0 0; margin-bottom: 2px; }
    .lotr-title { font-family: 'Uncial Antiqua', serif; font-size: 2.2em; color: #e6d7b6; letter-spacing: 2px; margin-bottom: 0; text-shadow: 0 2px 8px #7c5c1e; line-height: 1.1; }
    .lotr-title2 { font-family: 'Uncial Antiqua', serif; font-size: 2.5em; color: #e6d7b6; letter-spacing: 2px; margin-bottom: 12px; text-shadow: 0 2px 8px #7c5c1e; line-height: 1.1; }
    .param-block { margin: 20px 0; }
    label { display: block; margin: 5px 0; }
    input[type=range] { width: 120px; }
    .btn-row { display: flex; gap: 12px; justify-content: center; align-items: center; margin-bottom: 18px; margin-top: 12px; }
    .emoji-btn { font-size: 96px; width: 120px; height: 120px; background: #2d2617; border: 2px solid #d1b97a; border-radius: 18px; cursor: pointer; transition: filter 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 8px rgba(124,92,30,0.08); color: #e6d7b6; }
    .emoji-btn:hover { background: #3a2f1a; box-shadow: 0 4px 16px rgba(124,92,30,0.13); }
    .inactive { filter: grayscale(1); opacity: 0.6; }
    .ota-form input[type='file'] { margin-bottom: 8px; }
    .ota-form input[type='submit'] { background: #7c5c1e; color: #fff; border: none; border-radius: 8px; padding: 8px 18px; font-size: 1em; cursor: pointer; transition: background 0.2s; }
    .ota-form input[type='submit']:hover { background: #a07d3b; }
    .main-card hr { border: none; border-top: 1px solid #3a2f1a; margin: 24px 0; }
    select, input[type='range'] { border-radius: 6px; border: 1px solid #d1b97a; padding: 2px 6px; background: #23201a; color: #e6d7b6; margin-top: 4px; }
    input[type='checkbox'] { transform: scale(1.3); margin-right: 8px; }
    input[type='submit'] { background: #7c5c1e; color: #fff; border: none; border-radius: 8px; padding: 8px 18px; font-size: 1em; cursor: pointer; transition: background 0.2s; margin-top: 12px; }
    input[type='submit']:hover { background: #a07d3b; }
    .slider-label { display: flex; align-items: center; gap: 10px; font-size: 1em; font-weight: 400; }
    .slider-label > span, .slider-label > label, .slider-label > strong { margin-right: 10px; min-width: unset; }
    .slider-label select, .slider-label input[type="range"] { width: 100%; min-width: 0; max-width: 220px; flex: 1 1 0; }
    .param-row { display: flex; align-items: center; gap: 24px; margin: 32px 0 0 0; justify-content: flex-start; }
    .param-img { display: block; width: 100%; height: 500px; object-fit: cover; border-radius: 18px 18px 0 0; background: #222; margin: 0 auto; transition: filter 0.2s; }
    .param-controls { flex: 1 1 0; min-width: 160px; display: flex; flex-direction: column; gap: 18px; }
    .feature-card { background: #23201a; max-width: 480px; margin: 32px auto 0 auto; border-radius: 18px; box-shadow: 0 4px 24px rgba(0,0,0,0.18); padding: 24px 24px 32px 24px; overflow: hidden; margin-bottom: 32px; }
    .feature-card .param-row { margin: 0; }
    @media (max-width: 700px) { .param-row { flex-direction: column; align-items: stretch; gap: 12px; } .param-img { max-width: 100%; margin: 0 auto; } .param-controls { min-width: 0; } }
    @media (max-width: 600px) { .main-card { padding: 0 2vw 18px 2vw; } .header-img { height: 90px; } .lotr-title { font-size: 1.2em; } .lotr-title2 { font-size: 1.7em; } .emoji-btn { font-size: 48px; width: 64px; height: 64px; } .btn-row { gap: 12px; margin-bottom: 24px; margin-top: 16px; overflow-x: auto; } }
    #door-color-picker, #litho-color-picker { min-width: 40px; min-height: 40px; display: inline-block; }
  </style>
  <script src="/pickr.min.js"></script>
  <script>
    function setParam(param, value) {
      fetch('/set_param?' + param + '=' + encodeURIComponent(value), {method: 'GET'});
    }
    function rgbToHex(r, g, b) {
      return "#" + [r,g,b].map(x => x.toString(16).padStart(2, '0')).join('');
    }
    window.addEventListener('DOMContentLoaded', function() {
      var lithoSlider = document.querySelector('input[name="litho_bright"]');
      lithoSlider.addEventListener('input', function(e) {
        let perc = parseInt(e.target.value, 10);
        let hw = Math.round(perc * 127 / 100);
        setParam('litho_bright', hw);
      });
      var doorSlider = document.querySelector('input[name="door_bright"]');
      doorSlider.addEventListener('input', function(e) {
        let perc = parseInt(e.target.value, 10);
        let hw = Math.round(perc * 127 / 100);
        setParam('door_bright', hw);
      });
      document.querySelector('select[name="litho_anim"]').addEventListener('change', function(e) {
        setParam('litho_anim', e.target.value);
      });
      var lithoSpeedSlider = document.querySelector('input[name="litho_speed"]');
      lithoSpeedSlider.addEventListener('input', function(e) {
        let val = parseInt(e.target.value, 10);
        setParam('litho_speed', val);
      });
      var doorSpeedSlider = document.querySelector('input[name="door_speed"]');
      doorSpeedSlider.addEventListener('input', function(e) {
        let val = parseInt(e.target.value, 10);
        setParam('door_speed', val);
      });
      document.getElementById('door-img').addEventListener('click', function() {
        fetch('/toggle_doors', {method: 'POST'}).then(() => location.reload());
      });
      document.getElementById('litho-img').addEventListener('click', function() {
        setParam('litho_on', 1); // Динамика через API
      });
      if (document.getElementById('door-color-picker')) {
        const doorPickr = Pickr.create({
          el: '.color-picker',
          theme: 'nano',
          default: '#96ffff',
          defaultRepresentation: 'RGB',
          swatches: null,
          components: {
            preview: true,
            opacity: false,
            hue: true,
            interaction: {
              hex: false,
              rgba: false,
              hsla: false,
              hsva: false,
              cmyk: false,
              input: true,
              save: true,
              clear: false,
              cancel: false
            }
          }
        });
        doorPickr.on('save', (color, instance) => {
          const rgb = color.toRGBA();
          setParam('door_color', Math.round(rgb[0]) + ',' + Math.round(rgb[1]) + ',' + Math.round(rgb[2]));
          instance.hide();
        });
      }
      if (document.getElementById('litho-color-picker')) {
        const lithoPickr = Pickr.create({
          el: '.color-picker',
          theme: 'nano',
          default: '#ff9329',
          defaultRepresentation: 'RGB',
          swatches: null,
          components: {
            preview: true,
            opacity: false,
            hue: true,
            interaction: {
              hex: false,
              rgba: false,
              hsla: false,
              hsva: false,
              cmyk: false,
              input: true,
              save: true,
              clear: false,
              cancel: false
            }
          }
        });
        lithoPickr.on('save', (color, instance) => {
          const rgb = color.toRGBA();
          setParam('litho_color', Math.round(rgb[0]) + ',' + Math.round(rgb[1]) + ',' + Math.round(rgb[2]));
          instance.hide();
        });
      }
    });
  </script>
</head>
<body>
  <div class='main-card'>
    <img src="/LoTR-gold.png" alt="LoTR Gold" style="width:100%;height:auto;display:block;margin:0 auto 12px auto;max-height:160px;object-fit:contain;">
    <div class='center'>
      <div class='lotr-title'>Booknook</div>
      <div class='lotr-title2'>Durin's Doors</div>
      <div style='margin-top:10px;font-size:1.1em;'>Заряд 🔋: <span id='batt-pct'>--%</span></div>
    </div>
    <hr>
    <div class='btn-row'>
      <form method='POST' action='/toggle_doors'><button class='emoji-btn' type='submit'>🚪</button></form>
      <form method='POST' action='/toggle_litho'><button class='emoji-btn' type='submit'>👹</button></form>
      <form method='POST' action='/toggle_doorlight'><button class='emoji-btn' type='submit'>💡</button></form>
      <form method='POST' action='/play_wizard'><button class='emoji-btn' type='submit'>🧙</button></form>
    </div>
    <form>
      <div class='feature-card'>
        <img class='param-img' id='door-img' src='/black_doors.webp' alt='Двери' style='cursor:pointer;'>
        <div class='param-controls'>
          <label class='slider-label'>Яркость:
            <input type='range' name='door_bright' min='0' max='100' value='50'>
          </label>
          <label class='slider-label'>Скорость:
            <input type='range' name='door_speed' min='1' max='10' value='5'>
          </label>
          <label class='slider-label'>Цвет:
            <div id="door-color-picker" class="color-picker"></div>
          </label>
        </div>
      </div>
      <div class='feature-card'>
        <img class='param-img' id='litho-img' src='/balrog_for_LITHO.webp' alt='Литофания' style='cursor:pointer;'>
        <div class='param-controls'>
          <label class='slider-label'><span>Стиль:</span>
            <select name='litho_anim'>
              <option value='vertical_snake'>Вертикально</option>
              <option value='horizontal_snake'>Горизонтально</option>
              <option value='fade_in'>Плавно</option>
              <option value='random'>Случайно</option>
              <option value='diagonal'>Диагонально</option>
              <option value='alert'>Тревога</option>
            </select>
          </label>
          <label class='slider-label'>Яркость:
            <input type='range' name='litho_bright' min='0' max='100' value='50'>
          </label>
          <label class='slider-label'>Скорость:
            <input type='range' name='litho_speed' min='1' max='10' value='5'>
          </label>
          <label class='slider-label'>Цвет:
            <div id="litho-color-picker" class="color-picker"></div>
          </label>
        </div>
      </div>
    </form>
    <hr>
    <div class='center'>
      <input type="file" id="firmware">
      <button onclick="uploadFirmware()">Обновить</button>
      <div id="status"></div>
      <script>
        function uploadFirmware() {
          const fileInput = document.getElementById('firmware');
          if (!fileInput.files.length) return alert("Выберите файл");
          const file = fileInput.files[0];
          const form = new FormData();
          form.append("update", file);
          fetch("/update", {
            method: "POST",
            body: form
          })
          .then(res => {
            if (res.ok) {
              document.getElementById('status').innerText = "Успешно загружено. Перезапуск...";
              tryReconnect();
            } else {
              res.text().then(text => {
                document.getElementById('status').innerText = "Ошибка: " + res.status + " " + text;
              });
            }
          })
          .catch(err => {
            document.getElementById('status').innerText = "Ошибка: " + err.message;
          });
        }
        function tryReconnect() {
          let attempts = 0;
          const maxAttempts = 20;
          const statusDiv = document.getElementById('status');
          function check() {
            fetch("/", { cache: "no-store" })
              .then(r => {
                if (r.ok) {
                  statusDiv.innerText = "Устройство снова в сети! Перенаправление...";
                  setTimeout(() => location.href = "/", 1000);
                } else throw new Error();
              })
              .catch(() => {
                attempts++;
                if (attempts < maxAttempts) {
                  statusDiv.innerText = "Ожидание перезагрузки... (" + attempts + ")";
                  setTimeout(check, 2000);
                } else {
                  statusDiv.innerText = "Не удалось подключиться. Обновите страницу вручную.";
                }
              });
          }
          setTimeout(check, 3000);
        }
      </script>
    </div>
  </div>
</body>
</html> 
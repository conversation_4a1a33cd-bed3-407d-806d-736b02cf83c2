# Wiring Guide for BookNook LoTR Durin's Doors

## Power Supply

* Power source: 2×18650 Li-ion cells in parallel (4.2 V max, \~3.7 V nominal).
* Charging module: TP4056 with protection (HW-373).
* Main voltage boost: XL6019 step-up converter (to 5 V).
* Fuse: PPTC 4 A (JK30), placed between BAT+ and XL6019 IN (after TP4056 B+).

**Power path:**

```
Battery+ → TP4056 B+ → PPTC → XL6019 IN → 5V rail
```

**Ground is connected star-topology style, centered at the 5 V power rail.**

---

## WS2812B LED Strips

* Lithophane DATA: **GPIO 15**
* Left Door DATA: **GPIO 16**
* Right Door DATA: **GPIO 17**
* Data lines driven via **n-MOSFET buffers** (2N7000).
* Gate: driven from GPIO through 330–470 Ω resistor.
* Source → GND
* Drain → DATA line of WS2812B
* Each strip has:
  * 470 Ω resistor on DATA line (after the buffer)
  * 1000 µF capacitor between +5 V and GND near the strip
* Power wires: short (≤35 cm), ≥0.75 mm² cross-section.

---

## INMP441 Microphone

* Power: 3.3 V from ESP32
* GND: short wire to ESP32 GND
* Interface: I2S (GPIO32, GPIO33, GPIO25)

---

## MAX98357A Audio Amplifier

* Power: 5 V from XL6019
* GND: shared with ESP32
* I2S input: standard configuration
* Output: 1 speaker (polarity not critical)

---

## Servo Motors (MG90S)

* Power: 5 V from XL6019
* Control: GPIO 18 (left), GPIO 19 (right)
* Optional: add local 1000 µF capacitor per servo if you see brownouts or noise (not always needed)
* Optional: add flyback diode and switching MOSFET if using multiple servos

---

## TP4056 LED Indicators

* CHRG and STDBY pins from TP4056 pulled to GND when active
* External LEDs connected via 1 kΩ resistors
* LED cathodes tied to GND
* Connected to light indicators in power button

---

## Decoupling

* Only 1000 µF electrolytic capacitors used (no ceramics)
* One 1000 µF cap near:
  * LED strips
  * Servo motors (optional, see above)
  * XL6019 input/output (optional)

---

## GPIO Map

| GPIO | Function               | Notes                           |
| ---- | ---------------------- | ------------------------------- |
| 15   | LED strip `lithophane` | WS2812B                         |
| 16   | LED strip `door_L`     | WS2812B                         |
| 17   | LED strip `door_R`     | WS2812B                         |
| 19   | Servo control (1)      | MG90S left                      |
| 18   | Servo control (2)      | MG90S right                     |
| 25   | I2S (INMP441 WS)       | INMP441 WS (Word Select)        |
| 32   | I2S (INMP441 SD)       | INMP441 SD (Serial Data)        |
| 33   | I2S (INMP441 SCK)      | INMP441 SCK (Bit Clock)         |
| 12   | I2S (MAX98357A DIN)    | MAX98357A DIN (Data In)         |
| 14   | I2S (MAX98357A BCLK)   | MAX98357A BCLK (Bit Clock)      |
| 13   | I2S (MAX98357A LRC)    | MAX98357A LRC (Word Select)     |
| 32   | battery level          | resistors 100kOhm               |

---

## Additional Connections: Microphone & Amplifier Modules

### INMP441 Микрофон (I2S)
- **VDD:** Подключить к 3.3 V ESP32 (НЕ 5 V)
- **GND:** Короткий провод к GND ESP32
- **SD:** GPIO32
- **L/R:** К GND для левого канала или 3.3 V для правого (опционально)
- **WS:** GPIO25
- **SCK:** GPIO33

### MAX98357A Аудиоусилитель (I2S)
- **VIN:** 5 V от XL6019 (НЕ 3.3 V)
- **GND:** Общий с ESP32 и питанием
- **DIN:** GPIO12 (ESP32)
- **BCLK:** GPIO14 (ESP32)
- **LRC:** GPIO13 (ESP32)
- **GAIN:** Оставить не подключённым (стандартное усиление) или подключить к GND/3.3V/5V для изменения усиления
- **SD:** Оставить не подключённым (обычно всегда включён) или подключить к 3.3V/5V для включения
- **SPK+ / SPK-:** К динамику (4–8 Ом, полярность не критична)

#### Таблица подключения MAX98357A

| Контакт MAX98357A | Назначение         | Куда подключать           | Примечание                                  |
|-------------------|--------------------|---------------------------|---------------------------------------------|
| **LRC**           | Word Select (LRCK) | GPIO13 (ESP32)            | I2S LRC (Word Select)                       |
| **BCLK**          | Bit Clock          | GPIO14 (ESP32)            | I2S BCLK                                    |
| **DIN**           | Data In            | GPIO12 (ESP32)            | I2S Data                                    |
| **GAIN**          | Усиление           | GND/3.3V/5V или не подключать | Оставьте не подключённым для стандартного усиления. Подключите к GND, 3.3V или 5V для изменения уровня усиления (см. datasheet). |
| **SD**            | Shutdown           | 3.3V/5V или не подключать | Для включения усилителя: к 3.3V или 5V, либо оставить не подключённым (обычно всегда включён). |
| **GND**           | Земля              | GND (ESP32 и питание)     | Общий GND                                    |
| **VIN**           | Питание            | 5V (от XL6019)            | Не подключайте к 3.3V!                       |

#### Подключение динамика
- К двум выходам на плате (SPK+ и SPK-), полярность не критична, динамик 4–8 Ом.

#### Схема подключения (текстово)
- **VIN** → 5V (от XL6019)
- **GND** → GND (общий с ESP32)
- **DIN** → GPIO12 (ESP32)
- **BCLK** → GPIO14 (ESP32)
- **LRC** → GPIO13 (ESP32)
- **GAIN** → оставить не подключённым (или к GND/3.3V/5V для изменения усиления)
- **SD** → оставить не подключённым (или к 3.3V/5V для включения)
- **SPK+ / SPK-** → динамик

### SD Card Module (for future use)
- **Type:** Standard SD card module (not microSD)
- **Interface:** Most SD modules use SPI, but some support SDIO. This guide assumes SPI mode for compatibility.
- **VCC:** Connect to ESP32 3.3 V output (most modules support 3.3 V; check your module specs)
- **GND:** Connect to ESP32 GND
- **CS (Chip Select):** Recommended GPIO4 (not used elsewhere)
- **MOSI:** GPIO21 (not used elsewhere)
- **MISO:** GPIO27 (not used elsewhere)
- **SCK:** GPIO5 (not used elsewhere)
- **Note:**
  - Do not use GPIOs already assigned in the GPIO map above.
  - Use short, thick wires for SPI signals to minimize noise.
  - If your module only accepts 5 V, use a logic level shifter for all SPI lines.
  - Only one device should use each SPI CS line at a time.
  - Update your code to match the actual GPIOs used if you change these defaults.
  - If your SD module supports SDIO, refer to ESP32 SDMMC documentation for wiring and pin selection.

---

## Notes
* All GND connections should be common
* Use appropriate gauge wires for power connections
* Add decoupling capacitors (1000 µF) near LED strips and optionally servos
* Consider adding fuses for each LED strip
* Use level shifters (2N7000) for all LED strip data lines
* Keep signal wires away from power wires to reduce interference
* Parallel battery connection provides 3.7V with increased capacity (6000mAh total)
* Debounce the power button in software (recommended)
```
      BAT+ (4.2V)
           |
         [Source]
           |
         [Drain]----> PPTC----> XL6019 IN (5V)
           
      BAT+ (4.2V)
           |
         [Gate]
           |
         (1kΩ)
          /|\
         / | \
        /  |  \
       /   |   \
      /    |    \
     /     |     \
(Button) (100Ω)  (1kΩ)
   |       |       |
  GND   GPIO22  1000µF
                   |
                  GND

GPIO23 ---+ (connects to Gate Node, same as button side)
```
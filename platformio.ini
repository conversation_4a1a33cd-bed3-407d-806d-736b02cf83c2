[env:esp32dev]
platform = espressif32@^6.2.0
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_speed = 921600

lib_deps =
  adafruit/Adafruit NeoPixel
  https://github.com/madhephaestus/ESP32Servo.git
  bblanchon/ArduinoJson
  
    
build_flags =
    -DCORE_DEBUG_LEVEL=2 

board_build.partitions = my_partitions.csv
board_build.filesystem = spiffs
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L
monitor_port = /dev/cu.SLAB_USBtoUART
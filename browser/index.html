<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Browser inference demo</title>

    <style>
        #features {
            width: 50%;
            font-size: 18px;
        }

        #results {
            font-family: monospace;
            white-space: pre;
        }
    </style>
</head>
<body>
    <h1></h1>

    <p>
        <input type="text" id="features" placeholder="Paste your features here">
    </p>
    <p>
        <button id="run-inference">Run inference</button>
    </p>
    <p id="results"></p>

    <script src="edge-impulse-standalone.js"></script>
    <script src="run-impulse.js"></script>
    <script>
        (async () => {
            var classifier = new EdgeImpulseClassifier();
            await classifier.init();

            let project = classifier.getProjectInfo();
            document.querySelector('h1').textContent = project.owner + ' / ' + project.name + ' (version ' + project.deploy_version + ')';

            document.querySelector('#run-inference').onclick = () => {
                try {
                    let features = document.querySelector('#features').value.split(',').map(x => Number(x.trim()));
                    let res = classifier.classify(features);
                    document.querySelector('#results').textContent = JSON.stringify(res, null, 4);
                }
                catch (ex) {
                    alert('Failed to classify: ' + (ex.message || ex.toString()));
                }
            };
        })();
    </script>
</body>
</html>

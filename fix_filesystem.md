# Исправление ошибок файловой системы ESP32

## Проблемы и решения:

### 1. **LittleFS Corruption Error**
```
E (2009) esp_littlefs: Corrupted dir pair at {0x0, 0x1}
E (2010) esp_littlefs: mount failed, (-84)
```

**Причина**: Поврежденная или неинициализированная файловая система LittleFS.

### 2. **Core Dump Partition Missing**
```
E (320) esp_core_dump_flash: No core dump partition found!
```

**Причина**: Отсутствует раздел для сохранения дампов при критических ошибках.

## Исправления:

### ✅ **1. Добавлено автоматическое форматирование**
В код добавлена логика автоматического форматирования LittleFS при ошибке монтирования:

```cpp
if (!LittleFS.begin(true)) {
    // Принудительное форматирование при ошибке
    if (LittleFS.format()) {
        // Повторная попытка монтирования
        LittleFS.begin(false);
    }
}
```

### ✅ **2. Добавлен раздел Core Dump**
Обновлена таблица разделов:
- **spiffs**: уменьшен с 1200K до 1184K
- **coredump**: добавлен раздел 16K для дампов

### ✅ **3. Обновленная таблица разделов**
```csv
# Name,   Type, SubType, Offset,  Size, Flags
nvs,      data, nvs,     0x9000,  16K,
otadata,  data, ota,     0xd000,  8K,
app0,     app,  ota_0,   0x10000, 1280K,
app1,     app,  ota_1,   ,        1280K,
spiffs,   data, spiffs,  ,        1184K,
coredump, data, coredump,,        16K,
```

## Команды для исправления:

### **Вариант 1: Автоматическое исправление (рекомендуется)**
```bash
# Пересобрать и загрузить с новой таблицей разделов
pio run --target upload

# Код автоматически отформатирует LittleFS при первом запуске
```

### **Вариант 2: Ручная очистка (если автоматическое не помогло)**
```bash
# Полная очистка Flash памяти
pio run --target erase

# Загрузка прошивки с новой таблицей разделов
pio run --target upload

# Загрузка файловой системы (если есть файлы в data/)
pio run --target uploadfs
```

### **Вариант 3: Принудительное форматирование через esptool**
```bash
# Очистка только раздела файловой системы
esptool.py --port /dev/cu.SLAB_USBtoUART erase_region 0x290000 0x128000

# Или полная очистка
esptool.py --port /dev/cu.SLAB_USBtoUART erase_flash
```

## Проверка результата:

После загрузки прошивки в Serial Monitor должно появиться:
```
[INFO] SYSTEM: Starting initialization
[WARN] FS: Mount failed, trying format
[INFO] FS: Format successful, retrying mount
[INFO] FS: Mount successful after format
```

## Дополнительные рекомендации:

### **1. Создание папки data/ для файлов**
```bash
mkdir data
# Поместите сюда WAV файлы, HTML страницы и т.д.
```

### **2. Загрузка файлов в LittleFS**
```bash
pio run --target uploadfs
```

### **3. Мониторинг состояния файловой системы**
Добавьте в код проверку свободного места:
```cpp
size_t totalBytes = LittleFS.totalBytes();
size_t usedBytes = LittleFS.usedBytes();
Serial.printf("FS: %d/%d bytes used\n", usedBytes, totalBytes);
```

## Ожидаемый результат:

✅ LittleFS успешно монтируется  
✅ Веб-сервер запускается  
✅ Файлы доступны для чтения/записи  
✅ Core dump ошибки исчезают  

## Если проблемы остаются:

1. Проверьте качество USB кабеля
2. Попробуйте другой USB порт
3. Уменьшите upload_speed до 115200
4. Проверьте питание ESP32 (должно быть стабильным)

# 📖 Booknook: The Lord of the Rings Voice-Activated <PERSON>Nook

An interactive diorama powered by ESP32, using voice commands in Sindarin to create an immersive magical experience.

---

## 🧩 Modules & Components Used

### 🔋 Power & Regulation
- **2×18650 Li-ion Batteries (parallel)** – 3.7 V nominal, 3000 mAh each
- **TP4056 (HW-373)** – USB-C charging with over/under voltage protection
- **JK30 PPTC Fuse** – 4 A, 30 V for safety (placed between BAT+ and XL6019 IN)
- **XL6019 Boost Converter** – raises voltage to 5 V for main system

### 🧠 Microcontroller
- **ESP32-DevKitC** – main controller for logic, Wi-Fi, I2S audio, and NeoPixel control

### 🎙️ Microphone
- **INMP441** – digital I2S microphone (used with Edge Impulse KWS)

### 💡 Lighting
- **WS2812B RGB LED strips** (3 blocks):
  - `door_L` – 46 LEDs
  - `door_R` – 46 LEDs
  - `lithophane` – 32 LEDs

### ⚙️ Mechanical
- **Ser<PERSON> (x2)** – to open/close the doors (around 60° range)

---

## 🧠 Voice Commands & Actions

| 🗣️ Command     | 💬 Meaning         | 🧠 Action                                                        |
|---------------|--------------------|------------------------------------------------------------------|
| **"mellon"**  | *Friend*           | Opens both doors                                                 |
| **"not_pass"**| *Gandalf's warning*| Turns on lithophane with white/red pulsation                     |
| **"you_fools"**| *Famous quote*    | Fades out lithophane, closes doors, turns off all lights         |

---

## 🔧 Planned Behavior Flow

1. **Startup**
   - Push button powers on system via p-MOSFET latch
   - ESP32 boots, initializes peripherals
   - Wait 2 seconds
   - Cyan animation begins on both door LED strips
   - Wait 2 seconds
   - Doors open via servos
   - Wait 2 seconds
   - Lithophane lights up white @50%
   - Wait 2 seconds
   - Diagonal animation on lithophane

2. **Voice Recognition**
   - INMP441 feeds audio to Edge Impulse KWS
   - ESP32 listens for:
     - **"mellon"** → opens doors via servos
     - **"not_pass"** → turns on lithophane with white/red pulsation
     - **"you_fools"** → fade out lithophane, close doors, turn off all lights

---

## 🛠️ Notes
- Voice recognition handled offline via Edge Impulse KWS
- WS2812B strips require proper level shifting (2N7000) and decoupling
- Power-on circuit uses p-MOSFET SI2301A with RC latch and control pin from ESP32
/*
 * <PERSON><PERSON>'s Doors - Interactive Booknook Project (FULL FIX)
 * 
 * Поддержка:
 * - WS2812B: литофа<PERSON>ия (15), двери_<PERSON> (16), двери_<PERSON> (17)
 * - Сервоприводы: MG90S (19, 18)
 * - Аудио: MAX98357A (12, 14, 13) — воспроизведение
 * - OTA, LittleFS, Web UI
 */

#include <Adafruit_NeoPixel.h>
#include <ESP32Servo.h>
#include <WiFi.h>
#include <WebServer.h>
#include <Update.h>
#include <algorithm>
#include <vector>
#include <LittleFS.h>
#include <ArduinoJson.h>
#include <Battery.h>

// === I2S AUDIO (MAX98357A) ===
#include <driver/i2s.h>

// === Литофания: 4x8 ===
#define LITHO_ROWS 4
#define LITHO_COLS 8
#define NUM_LITHO (LITHO_ROWS * LITHO_COLS)
#define NUM_DOOR 46  // Количество светодиодов в ленте дверей

// === Pin Definitions ===
#define LITHOPHANE_PIN 15     // Лента литофании
#define DOOR_L_PIN 16         // Лента левой двери
#define DOOR_R_PIN 17         // Лента правой двери
#define SERVO_L_PIN 19        // Серво левой двери
#define SERVO_R_PIN 18        // Серво правой двери

// === I2S Pins (MAX98357A) ===
#define I2S_DOUT 12
#define I2S_BCLK 14
#define I2S_LRC  13

// === Servo Configuration ===
const int leftClosed = 0;
const int leftOpen = 60;   // 60° влево
const int rightClosed = 60;
const int rightOpen = 0;   // 60° вправо

// === Create LED strips ===
Adafruit_NeoPixel lithophane(NUM_LITHO, LITHOPHANE_PIN, NEO_GRB + NEO_KHZ800);
Adafruit_NeoPixel door_L(NUM_DOOR, DOOR_L_PIN, NEO_GRB + NEO_KHZ800);
Adafruit_NeoPixel door_R(NUM_DOOR, DOOR_R_PIN, NEO_GRB + NEO_KHZ800);

// === Create Servos ===
Servo servo_L;
Servo servo_R;

// === WiFi AP credentials ===
const char* ssid = "BookNook-AP";
const char* password = "12345678";
WebServer server(80);

// === Глобальные переменные ===
bool isWizardPlaying = false;
unsigned long wizardPlayEndMs = 0;

// === Глобальные параметры ===
bool litho_on = false;
bool door_light_on = false;
uint8_t litho_bright = 64; // 0-127 (50%)
uint8_t door_bright = 64;  // 0-127 (50%)
String litho_anim = "fade_in";
bool doors_open = false;
uint8_t litho_speed = 5; // 1-10
uint8_t door_speed = 5; // 1-10

// === Цвета подсветки ===
uint8_t litho_r = 255, litho_g = 147, litho_b = 41; // тёплый белый
uint8_t door_r = 150, door_g = 255, door_b = 255;   // циан

// === Battery monitoring ===
Battery battery(3000, 4200, 34, 2.0); // minV, maxV, analog pin, divider
float battery_voltage = 0.0;
int battery_percent = 0;

// === Прототипы функций (важно для компиляции) ===
void playWavFromFS(const char* filename);

// === State machine for lithophane animation ===
struct LithoAnimState {
  String current_anim = "fade_in";
  bool running = false;
  unsigned long lastUpdate = 0;
  int step1 = 0;
  int step2 = 0;
  std::vector<int> random_idxs;
};
LithoAnimState lithoState;

void resetLithoAnim() {
  lithoState.current_anim = litho_anim;
  lithoState.running = true;
  lithoState.lastUpdate = millis();
  lithoState.step1 = 0;
  lithoState.step2 = 0;
  lithoState.random_idxs.clear();

  if (litho_anim == "alert") {
    lithoState.step1 = 0;
    lithoState.step2 = 0;
    lithoState.random_idxs.resize(NUM_LITHO);
    for (int i = 0; i < NUM_LITHO; i++) lithoState.random_idxs[i] = i;
  }
  if (litho_anim == "random") {
    lithoState.random_idxs.resize(NUM_LITHO);
    for (int i = 0; i < NUM_LITHO; i++) lithoState.random_idxs[i] = i;
    std::random_shuffle(lithoState.random_idxs.begin(), lithoState.random_idxs.end());
  }
}

void litho_anim_step() {
  unsigned long now = millis();
  int speed_map[11] = {0, 200, 150, 120, 90, 70, 55, 40, 30, 20, 10};
  int delayMs = speed_map[litho_speed];
  if (delayMs == 0) delayMs = 70;
  if (!lithoState.running || now - lithoState.lastUpdate < delayMs) return;
  lithoState.lastUpdate = now;

  if (litho_anim == "vertical_snake") {
    if (lithoState.step1 >= LITHO_COLS) { lithoState.running = false; return; }
    for (int row = 0; row < LITHO_ROWS; row++) {
      int idx = row * LITHO_COLS + lithoState.step1;
      lithophane.setPixelColor(idx, lithophane.Color((255 * litho_bright) / 127, (147 * litho_bright) / 127, (41 * litho_bright) / 127));
    }
    lithophane.show();
    lithoState.step1++;
  }
  else if (litho_anim == "horizontal_snake") {
    if (lithoState.step1 >= LITHO_ROWS) { lithoState.running = false; return; }
    for (int col = 0; col < LITHO_COLS; col++) {
      int idx = lithoState.step1 * LITHO_COLS + col;
      lithophane.setPixelColor(idx, lithophane.Color((255 * litho_bright) / 127, (147 * litho_bright) / 127, (41 * litho_bright) / 127));
    }
    lithophane.show();
    lithoState.step1++;
  }
  else if (litho_anim == "fade_in") {
    if (lithoState.step1 > litho_bright) { lithoState.running = false; return; }
    for (int i = 0; i < NUM_LITHO; i++) {
      lithophane.setPixelColor(i, lithophane.Color((255 * lithoState.step1) / 127, (147 * lithoState.step1) / 127, (41 * lithoState.step1) / 127));
    }
    lithophane.show();
    lithoState.step1 += 2;
  }
  else if (litho_anim == "random") {
    if (lithoState.step1 >= NUM_LITHO) { lithoState.running = false; return; }
    int idx = lithoState.random_idxs[lithoState.step1];
    lithophane.setPixelColor(idx, lithophane.Color((255 * litho_bright) / 127, (147 * litho_bright) / 127, (41 * litho_bright) / 127));
    lithophane.show();
    lithoState.step1++;
  }
  else if (litho_anim == "diagonal") {
    if (lithoState.step1 >= LITHO_ROWS + LITHO_COLS - 1) { lithoState.running = false; return; }
    for (int row = 0; row < LITHO_ROWS; row++) {
      int col = lithoState.step1 - row;
      if (col >= 0 && col < LITHO_COLS) {
        int idx = row * LITHO_COLS + col;
        lithophane.setPixelColor(idx, lithophane.Color((255 * litho_bright) / 127, (147 * litho_bright) / 127, (41 * litho_bright) / 127));
      }
    }
    lithophane.show();
    lithoState.step1++;
  }
  else if (litho_anim == "alert") {
    if (lithoState.step2 == 0) {
      if (lithoState.step1 >= LITHO_COLS) {
        lithoState.step2 = 1;
        return;
      }
      for (int row = 0; row < LITHO_ROWS; row++) {
        for (int col = 0; col <= lithoState.step1; col++) {
          int idx = row * LITHO_COLS + col;
          if ((col % 2) == 0)
            lithophane.setPixelColor(idx, lithophane.Color((255 * litho_bright) / 127, (147 * litho_bright) / 127, (41 * litho_bright) / 127));
          else
            lithophane.setPixelColor(idx, lithophane.Color((255 * litho_bright) / 127, 0, 0));
        }
      }
      lithophane.show();
      lithoState.step1++;
    }
    else if (lithoState.step2 == 1) {
      for (int i = 0; i < NUM_LITHO; i++) {
        if (random(0, 4) == 0) {
          bool isRed = random(0, 2);
          uint8_t bright = random(25, 64);
          if (isRed)
            lithophane.setPixelColor(i, lithophane.Color((255 * bright) / 127, 0, 0));
          else
            lithophane.setPixelColor(i, lithophane.Color((255 * bright) / 127, (147 * bright) / 127, (41 * bright) / 127));
        }
      }
      lithophane.show();
    }
  }
}

// === State machine for door animation (плавное открытие) ===
struct DoorAnimState {
  bool running = false;
  bool target_open = false;
  unsigned long lastUpdate = 0;
  int current_angle = 0;
};
DoorAnimState doorState;

void resetDoorAnim(bool open) {
  doorState.target_open = open;
  doorState.running = true;
  doorState.lastUpdate = millis();
  doorState.current_angle = open ? leftClosed : leftOpen;
}

void door_anim_step() {
  if (!doorState.running) return;
  unsigned long now = millis();
  int speed_map[11] = {0, 200, 150, 120, 90, 70, 55, 40, 30, 20, 10};
  int delayMs = speed_map[door_speed];
  if (delayMs == 0) delayMs = 70;
  if (now - doorState.lastUpdate < delayMs) return;
  doorState.lastUpdate = now;

  int target = doorState.target_open ? leftOpen : leftClosed;
  if (doorState.current_angle == target) {
    doorState.running = false;
    return;
  }

  int step = (target > doorState.current_angle) ? 1 : -1;
  doorState.current_angle += step;
  servo_L.write(doorState.current_angle);
  servo_R.write(map(doorState.current_angle, leftClosed, leftOpen, rightClosed, rightOpen));
}

// === Управление подсветкой дверей ===
void setDoorLights(bool on, uint8_t bright) {
  uint32_t color = door_L.Color(
    (door_r * bright) / 127,
    (door_g * bright) / 127,
    (door_b * bright) / 127
  );
  for (int i = 0; i < NUM_DOOR; i++) {
    door_L.setPixelColor(i, on ? color : 0);
    door_R.setPixelColor(i, on ? color : 0);
  }
  door_L.show();
  door_R.show();
}

// === HTML страница (остаётся без изменений) ===
const char* htmlPage = R"rawliteral(
<!DOCTYPE html>
<html>
<head>
  <title>Booknook Durin's Doors</title>
  <meta charset="UTF-8">
  <meta name='viewport' content='width=device-width,initial-scale=1'>
  <link href='/uncial-antiqua.css' rel='stylesheet'>
  <link rel="stylesheet" href="/nano.min.css">
  <style>
    body {
      background: #181818;
      color: #f7f5ed;
      font-family: 'Segoe UI', Arial, sans-serif;
      margin: 0;
      padding: 0;
    }
    .center {
      text-align: center;
      margin-top: 0;
    }
    .main-card {
      background: #23201a;
      max-width: 480px;
      margin: 32px auto 0 auto;
      border-radius: 18px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.18);
      padding: 0 24px 48px 24px;
      overflow: hidden;
    }
    .header-img {
      width: 100%;
      height: 160px;
      background: url('/LoTR-gold.svg') center center/contain no-repeat;
      border-radius: 18px 18px 0 0;
      margin-bottom: 2px;
    }
    .lotr-title {
      font-family: 'Uncial Antiqua', serif;
      font-size: 2.2em;
      color: #e6d7b6;
      letter-spacing: 2px;
      margin-bottom: 0;
      text-shadow: 0 2px 8px #7c5c1e;
      line-height: 1.1;
    }
    .lotr-title2 {
      font-family: 'Uncial Antiqua', serif;
      font-size: 2.5em;
      color: #e6d7b6;
      letter-spacing: 2px;
      margin-bottom: 12px;
      text-shadow: 0 2px 8px #7c5c1e;
      line-height: 1.1;
    }
    .param-block {
      margin: 20px 0;
    }
    label { display: block; margin: 5px 0; }
    input[type=range] { width: 120px; }
    .btn-row {
      display: flex;
      gap: 12px;
      justify-content: center;
      align-items: center;
      margin-bottom: 18px;
      margin-top: 12px;
    }
    .emoji-btn {
      font-size: 96px;
      width: 120px;
      height: 120px;
      background: #2d2617;
      border: 2px solid #d1b97a;
      border-radius: 18px;
      cursor: pointer;
      transition: filter 0.2s, box-shadow 0.2s, background 0.2s;
      box-shadow: 0 2px 8px rgba(124,92,30,0.08);
      color: #e6d7b6;
    }
    .emoji-btn:hover {
      background: #3a2f1a;
      box-shadow: 0 4px 16px rgba(124,92,30,0.13);
    }
    .inactive { filter: grayscale(1); opacity: 0.6; }
    .ota-form input[type='file'] {
      margin-bottom: 8px;
    }
    .ota-form input[type='submit'] {
      background: #7c5c1e;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 8px 18px;
      font-size: 1em;
      cursor: pointer;
      transition: background 0.2s;
    }
    .ota-form input[type='submit']:hover {
      background: #a07d3b;
    }
    .main-card hr {
      border: none;
      border-top: 1px solid #3a2f1a;
      margin: 24px 0;
    }
    select, input[type='range'] {
      border-radius: 6px;
      border: 1px solid #d1b97a;
      padding: 2px 6px;
      background: #23201a;
      color: #e6d7b6;
      margin-top: 4px;
    }
    input[type='checkbox'] {
      transform: scale(1.3);
      margin-right: 8px;
    }
    input[type='submit'] {
      background: #7c5c1e;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 8px 18px;
      font-size: 1em;
      cursor: pointer;
      transition: background 0.2s;
      margin-top: 12px;
    }
    input[type='submit']:hover {
      background: #a07d3b;
    }
    .slider-label {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 1em;
      font-weight: 400;
    }
    .slider-label > span,
    .slider-label > label,
    .slider-label > strong {
      margin-right: 10px;
      min-width: unset;
    }
    .slider-label select,
    .slider-label input[type="range"] {
      width: 100%;
      min-width: 0;
      max-width: 220px;
      flex: 1 1 0;
    }
    .param-row {
      display: flex;
      align-items: center;
      gap: 24px;
      margin: 32px 0 0 0;
      justify-content: flex-start;
    }
    .param-img {
      display: block;
      width: 100%;
      height: 500px;
      object-fit: cover;
      border-radius: 18px 18px 0 0;
      background: #222;
      margin: 0 auto;
      transition: filter 0.2s;
    }
    .param-controls {
      flex: 1 1 0;
      min-width: 160px;
      display: flex;
      flex-direction: column;
      gap: 18px;
    }
    .feature-card {
      background: #23201a;
      max-width: 480px;
      margin: 32px auto 0 auto;
      border-radius: 18px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.18);
      padding: 24px 24px 32px 24px;
      overflow: hidden;
      margin-bottom: 32px;
    }
    .feature-card .param-row {
      margin: 0;
    }
    @media (max-width: 700px) {
      .param-row { flex-direction: column; align-items: stretch; gap: 12px; }
      .param-img { max-width: 100%; margin: 0 auto; }
      .param-controls { min-width: 0; }
    }
    @media (max-width: 600px) {
      .main-card { padding: 0 2vw 18px 2vw; }
      .header-img { height: 90px; }
      .lotr-title { font-size: 1.2em; }
      .lotr-title2 { font-size: 1.7em; }
      .emoji-btn { font-size: 48px; width: 64px; height: 64px; }
      .btn-row {
        gap: 12px;
        margin-bottom: 24px;
        margin-top: 16px;
        overflow-x: auto;
      }
    }
    #door-color-picker, #litho-color-picker {
      min-width: 40px;
      min-height: 40px;
      display: inline-block;
    }
  </style>
  <script src="/pickr.min.js"></script>
  <script>
    function setParam(param, value) {
      fetch('/set_param?' + param + '=' + encodeURIComponent(value), {method: 'GET'});
    }
    function rgbToHex(r, g, b) {
      return "#" + [r,g,b].map(x => x.toString(16).padStart(2, '0')).join('');
    }
    window.addEventListener('DOMContentLoaded', function() {
      var lithoSlider = document.querySelector('input[name="litho_bright"]');
      lithoSlider.addEventListener('input', function(e) {
        let perc = parseInt(e.target.value, 10);
        let hw = Math.round(perc * 127 / 100);
        setParam('litho_bright', hw);
      });
      var doorSlider = document.querySelector('input[name="door_bright"]');
      doorSlider.addEventListener('input', function(e) {
        let perc = parseInt(e.target.value, 10);
        let hw = Math.round(perc * 127 / 100);
        setParam('door_bright', hw);
      });
      document.querySelector('select[name="litho_anim"]').addEventListener('change', function(e) {
        setParam('litho_anim', e.target.value);
      });
      var lithoSpeedSlider = document.querySelector('input[name="litho_speed"]');
      lithoSpeedSlider.addEventListener('input', function(e) {
        let val = parseInt(e.target.value, 10);
        setParam('litho_speed', val);
      });
      var doorSpeedSlider = document.querySelector('input[name="door_speed"]');
      doorSpeedSlider.addEventListener('input', function(e) {
        let val = parseInt(e.target.value, 10);
        setParam('door_speed', val);
      });
      document.getElementById('door-img').addEventListener('click', function() {
        fetch('/toggle_doors', {method: 'POST'}).then(() => location.reload());
      });
      document.getElementById('litho-img').addEventListener('click', function() {
        setParam('litho_on', (%LITHO_ON% ? 0 : 1));
      });
      if (document.getElementById('door-color-picker')) {
        const doorPickr = Pickr.create({
          el: '.color-picker',
          theme: 'nano',
          default: '#%DOOR_COLOR_HEX%',
          defaultRepresentation: 'RGB',
          swatches: null,
          components: {
            preview: true,
            opacity: false,
            hue: true,
            interaction: {
              hex: false,
              rgba: false,
              hsla: false,
              hsva: false,
              cmyk: false,
              input: true,
              save: true,
              clear: false,
              cancel: false
            }
          }
        });
        doorPickr.on('save', (color, instance) => {
          const rgb = color.toRGBA();
          setParam('door_color', Math.round(rgb[0]) + ',' + Math.round(rgb[1]) + ',' + Math.round(rgb[2]));
          instance.hide();
        });
      }
      if (document.getElementById('litho-color-picker')) {
        const lithoPickr = Pickr.create({
          el: '.color-picker',
          theme: 'nano',
          default: '#%LITHO_COLOR_HEX%',
          defaultRepresentation: 'RGB',
          swatches: null,
          components: {
            preview: true,
            opacity: false,
            hue: true,
            interaction: {
              hex: false,
              rgba: false,
              hsla: false,
              hsva: false,
              cmyk: false,
              input: true,
              save: true,
              clear: false,
              cancel: false
            }
          }
        });
        lithoPickr.on('save', (color, instance) => {
          const rgb = color.toRGBA();
          setParam('litho_color', Math.round(rgb[0]) + ',' + Math.round(rgb[1]) + ',' + Math.round(rgb[2]));
          instance.hide();
        });
      }
    });
  </script>
</head>
<body>
  <div class='main-card'>
    <div class='header-img'></div>
    <div class='center'>
      <div class='lotr-title'>Booknook</div>
      <div class='lotr-title2'>Durin's Doors</div>
      <div style='margin-top:10px;font-size:1.1em;'>Заряд 🔋: <span id='batt-pct'>%BATT_PCT%%</span></span></div>
    </div>
    <hr>
    <div class='btn-row'>
      <form method='POST' action='/toggle_doors'><button class='%DOORS_BTN_CLASS%' type='submit'>🚪</button></form>
      <form method='POST' action='/toggle_litho'><button class='%LITHO_BTN_CLASS%' type='submit'>👹</button></form>
      <form method='POST' action='/toggle_doorlight'><button class='%DOORLIGHT_BTN_CLASS%' type='submit'>💡</button></form>
      <form method='POST' action='/play_wizard'><button class='%WIZARD_BTN_CLASS%' type='submit'>🧙</button></form>
    </div>
    <form>
      <div class='feature-card'>
        <img class='param-img' id='door-img' src='/black_doors.webp' alt='Двери' style='cursor:pointer;'>
        <div class='param-controls'>
          <label class='slider-label'>Яркость:
            <input type='range' name='door_bright' min='0' max='100' value='%DOOR_BRIGHT_PERC%'>
          </label>
          <label class='slider-label'>Скорость:
            <input type='range' name='door_speed' min='1' max='10' value='%DOOR_SPEED%'>
          </label>
          <label class='slider-label'>Цвет:
            <div id="door-color-picker" class="color-picker"></div>
          </label>
        </div>
      </div>
      <div class='feature-card'>
        <img class='param-img' id='litho-img' src='/balrog_for_LITHO.webp' alt='Литофания' style='cursor:pointer;'>
        <div class='param-controls'>
          <label class='slider-label'><span>Стиль:</span>
            <select name='litho_anim'>
              <option value='vertical_snake' %A1%>Вертикально</option>
              <option value='horizontal_snake' %A2%>Горизонтально</option>
              <option value='fade_in' %A3%>Плавно</option>
              <option value='random' %A4%>Случайно</option>
              <option value='diagonal' %A5%>Диагонально</option>
              <option value='alert' %A6%>Тревога</option>
            </select>
          </label>
          <label class='slider-label'>Яркость:
            <input type='range' name='litho_bright' min='0' max='100' value='%LITHO_BRIGHT_PERC%'>
          </label>
          <label class='slider-label'>Скорость:
            <input type='range' name='litho_speed' min='1' max='10' value='%LITHO_SPEED%'>
          </label>
          <label class='slider-label'>Цвет:
            <div id="litho-color-picker" class="color-picker"></div>
          </label>
        </div>
      </div>
    </form>
    <hr>
    <div class='center'>
      <input type="file" id="firmware">
      <button onclick="uploadFirmware()">Обновить</button>
      <div id="status"></div>
      <script>
        function uploadFirmware() {
          const fileInput = document.getElementById('firmware');
          if (!fileInput.files.length) return alert("Выберите файл");
          const file = fileInput.files[0];
          const form = new FormData();
          form.append("update", file);
          fetch("/update", {
            method: "POST",
            body: form
          })
          .then(res => {
            if (res.ok) {
              document.getElementById('status').innerText = "Успешно загружено. Перезапуск...";
              tryReconnect();
            } else {
              res.text().then(text => {
                document.getElementById('status').innerText = "Ошибка: " + res.status + " " + text;
              });
            }
          })
          .catch(err => {
            document.getElementById('status').innerText = "Ошибка: " + err.message;
          });
        }
        function tryReconnect() {
          let attempts = 0;
          const maxAttempts = 20;
          const statusDiv = document.getElementById('status');
          function check() {
            fetch("/", { cache: "no-store" })
              .then(r => {
                if (r.ok) {
                  statusDiv.innerText = "Устройство снова в сети! Перенаправление...";
                  setTimeout(() => location.href = "/", 1000);
                } else throw new Error();
              })
              .catch(() => {
                attempts++;
                if (attempts < maxAttempts) {
                  statusDiv.innerText = "Ожидание перезагрузки... (" + attempts + ")";
                  setTimeout(check, 2000);
                } else {
                  statusDiv.innerText = "Не удалось подключиться. Обновите страницу вручную.";
                }
              });
          }
          setTimeout(check, 3000);
        }
      </script>
    </div>
  </div>
</body>
</html>
)rawliteral";

String processor(const String& var) {
  if (var == "LITHO_BRIGHT") return String(litho_bright);
  if (var == "DOOR_BRIGHT") return String(door_bright);
  if (var == "LITHO_BRIGHT_PERC") return String((litho_bright * 100) / 127);
  if (var == "DOOR_BRIGHT_PERC") return String((door_bright * 100) / 127);
  if (var == "LITHO_SPEED") return String(litho_speed);
  if (var == "DOOR_SPEED") return String(door_speed);
  if (var == "A1") return litho_anim == "vertical_snake" ? "selected" : "";
  if (var == "A2") return litho_anim == "horizontal_snake" ? "selected" : "";
  if (var == "A3") return litho_anim == "fade_in" ? "selected" : "";
  if (var == "A4") return litho_anim == "random" ? "selected" : "";
  if (var == "A5") return litho_anim == "diagonal" ? "selected" : "";
  if (var == "A6") return litho_anim == "alert" ? "selected" : "";
  if (var == "DOORS_BTN_CLASS") return doors_open ? "emoji-btn" : "emoji-btn inactive";
  if (var == "LITHO_BTN_CLASS") return litho_on ? "emoji-btn" : "emoji-btn inactive";
  if (var == "DOORLIGHT_BTN_CLASS") return door_light_on ? "emoji-btn" : "emoji-btn inactive";
  if (var == "WIZARD_BTN_CLASS") return isWizardPlaying ? "emoji-btn" : "emoji-btn inactive";
  if (var == "LITHO_ON") return litho_on ? "1" : "0";
  if (var == "BATT_PCT") return String(battery_percent);
  if (var == "BATT_VOLT") return String(battery_voltage, 2);
  return String();
}

void handleRoot() {
  String page = htmlPage;
  page.replace("%LITHO_BRIGHT%", processor("LITHO_BRIGHT"));
  page.replace("%DOOR_BRIGHT%", processor("DOOR_BRIGHT"));
  page.replace("%LITHO_BRIGHT_PERC%", processor("LITHO_BRIGHT_PERC"));
  page.replace("%DOOR_BRIGHT_PERC%", processor("DOOR_BRIGHT_PERC"));
  page.replace("%LITHO_SPEED%", processor("LITHO_SPEED"));
  page.replace("%DOOR_SPEED%", processor("DOOR_SPEED"));
  page.replace("%A1%", processor("A1"));
  page.replace("%A2%", processor("A2"));
  page.replace("%A3%", processor("A3"));
  page.replace("%A4%", processor("A4"));
  page.replace("%A5%", processor("A5"));
  page.replace("%A6%", processor("A6"));
  page.replace("%DOORS_BTN_CLASS%", processor("DOORS_BTN_CLASS"));
  page.replace("%LITHO_BTN_CLASS%", processor("LITHO_BTN_CLASS"));
  page.replace("%DOORLIGHT_BTN_CLASS%", processor("DOORLIGHT_BTN_CLASS"));
  page.replace("%WIZARD_BTN_CLASS%", processor("WIZARD_BTN_CLASS"));
  page.replace("%LITHO_ON%", processor("LITHO_ON"));
  page.replace("%BATT_PCT%", String(battery_percent));
  page.replace("%BATT_VOLT%", String(battery_voltage, 2));
  page.replace("%DOOR_COLOR_HEX%", String(door_r, HEX) + String(door_g, HEX) + String(door_b, HEX));
  page.replace("%LITHO_COLOR_HEX%", String(litho_r, HEX) + String(litho_g, HEX) + String(litho_b, HEX));
  server.send(200, "text/html", page);
}

#define PARAMS_FILE "/params.json"
void saveParamsToFS() {
  File f = LittleFS.open(PARAMS_FILE, "w");
  if (!f) return;
  StaticJsonDocument<512> doc;
  doc["litho_on"] = litho_on;
  doc["door_light_on"] = door_light_on;
  doc["doors_open"] = doors_open;
  doc["litho_bright"] = litho_bright;
  doc["door_bright"] = door_bright;
  doc["litho_anim"] = litho_anim;
  doc["litho_speed"] = litho_speed;
  doc["door_speed"] = door_speed;
  doc["litho_r"] = litho_r;
  doc["litho_g"] = litho_g;
  doc["litho_b"] = litho_b;
  doc["door_r"] = door_r;
  doc["door_g"] = door_g;
  doc["door_b"] = door_b;
  serializeJson(doc, f);
  f.close();
}

void loadParamsFromFS() {
  File f = LittleFS.open(PARAMS_FILE, "r");
  if (!f) return;
  StaticJsonDocument<512> doc;
  DeserializationError err = deserializeJson(doc, f);
  if (err) { f.close(); return; }
  if (doc.containsKey("litho_on")) litho_on = doc["litho_on"];
  if (doc.containsKey("door_light_on")) door_light_on = doc["door_light_on"];
  if (doc.containsKey("doors_open")) doors_open = doc["doors_open"];
  if (doc.containsKey("litho_bright")) litho_bright = doc["litho_bright"];
  if (doc.containsKey("door_bright")) door_bright = doc["door_bright"];
  if (doc.containsKey("litho_anim")) litho_anim = (const char*)doc["litho_anim"];
  if (doc.containsKey("litho_speed")) litho_speed = doc["litho_speed"];
  if (doc.containsKey("door_speed")) door_speed = doc["door_speed"];
  if (doc.containsKey("litho_r")) litho_r = doc["litho_r"];
  if (doc.containsKey("litho_g")) litho_g = doc["litho_g"];
  if (doc.containsKey("litho_b")) litho_b = doc["litho_b"];
  if (doc.containsKey("door_r")) door_r = doc["door_r"];
  if (doc.containsKey("door_g")) door_g = doc["door_g"];
  if (doc.containsKey("door_b")) door_b = doc["door_b"];
  f.close();
}

void handleSetParam() {
  if (server.hasArg("litho_bright")) litho_bright = server.arg("litho_bright").toInt();
  if (server.hasArg("door_bright")) door_bright = server.arg("door_bright").toInt();
  if (server.hasArg("litho_anim")) litho_anim = server.arg("litho_anim");
  if (server.hasArg("litho_speed")) litho_speed = server.arg("litho_speed").toInt();
  if (server.hasArg("door_speed")) door_speed = server.arg("door_speed").toInt();
  if (server.hasArg("litho_color")) {
    String c = server.arg("litho_color");
    int r, g, b;
    if (sscanf(c.c_str(), "%d,%d,%d", &r, &g, &b) == 3) {
      litho_r = r; litho_g = g; litho_b = b;
    }
  }
  if (server.hasArg("door_color")) {
    String c = server.arg("door_color");
    int r, g, b;
    if (sscanf(c.c_str(), "%d,%d,%d", &r, &g, &b) == 3) {
      door_r = r; door_g = g; door_b = b;
    }
  }
  if (server.hasArg("litho_on")) litho_on = server.arg("litho_on") == "1";
  if (server.hasArg("door_light_on")) door_light_on = server.arg("door_light_on") == "1";
  saveParamsToFS();
  server.send(200, "text/plain", "OK");
}

// ✅ Исправленный обработчик OTA
void handleUpdate() {
  HTTPUpload& upload = server.upload();
  Serial.printf("OTA: %s, size: %d, status: %d\n", upload.filename.c_str(), upload.currentSize, upload.status);

  if (upload.status == UPLOAD_FILE_START) {
    Serial.println("Начало OTA-обновления...");
    if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
      Update.printError(Serial);
      server.send(500, "text/plain", "OTA: Ошибка начала");
      return;
    }
  } else if (upload.status == UPLOAD_FILE_WRITE) {
    if (Update.write(upload.buf, upload.currentSize) != upload.currentSize) {
      Update.printError(Serial);
      server.send(500, "text/plain", "OTA: Ошибка записи");
      return;
    }
  } else if (upload.status == UPLOAD_FILE_END) {
    if (Update.end(true)) {
      Serial.println("OTA: Успешно завершено");
      server.send(200, "text/html", 
        "<html><body>"
        "<h2>Обновление завершено!</h2>"
        "<p>Устройство перезагружается...</p>"
        "<script>setTimeout(function(){window.location.href='/'},6000);</script>"
        "</body></html>");
      delay(100);
      ESP.restart();
    } else {
      Update.printError(Serial);
      server.send(500, "text/plain", "OTA: Ошибка завершения");
    }
  } else if (upload.status == UPLOAD_FILE_ABORTED) {
    Update.end();
    Serial.println("OTA: Отменено");
  }
}

void handleToggleDoors() {
  doors_open = !doors_open;
  resetDoorAnim(doors_open);
  server.sendHeader("Location", "/");
  server.send(303);
}

void handleToggleLitho() {
  litho_on = !litho_on;
  server.sendHeader("Location", "/");
  server.send(303);
}

void handleToggleDoorLight() {
  door_light_on = !door_light_on;
  server.sendHeader("Location", "/");
  server.send(303);
}

void handlePlayWizard() {
  isWizardPlaying = true;
  wizardPlayEndMs = millis() + 3000;
  server.sendHeader("Location", "/");
  server.send(303);
  playWavFromFS("/you-shall-not-pass-RU.wav");
}

void handleFileRequest() {
  String path = server.uri();
  File file = LittleFS.open(path, "r");
  if (!file) {
    server.send(404, "text/plain", "File not found: " + path);
    return;
  }
  String contentType = "text/plain";
  if (path.endsWith(".svg")) contentType = "image/svg+xml";
  else if (path.endsWith(".webp")) contentType = "image/webp";
  else if (path.endsWith(".css")) contentType = "text/css";
  else if (path.endsWith(".js")) contentType = "application/javascript";
  else if (path.endsWith(".woff2")) contentType = "font/woff2";
  else if (path.endsWith(".wav")) contentType = "audio/wav";

  if (path.endsWith(".css") || path.endsWith(".js") || path.endsWith(".svg") ||
      path.endsWith(".webp") || path.endsWith(".woff2")) {
    server.sendHeader("Cache-Control", "public, max-age=2592000");
  } else {
    server.sendHeader("Cache-Control", "no-cache, no-store, must-revalidate");
  }
  server.streamFile(file, contentType);
  file.close();
}

// === I2S AUDIO (MAX98357A) ===
void setupI2S() {
  i2s_config_t i2s_config = {
    .mode = (i2s_mode_t)(I2S_MODE_MASTER | I2S_MODE_TX),
    .sample_rate = 16000,
    .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
    .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,
    .communication_format = I2S_COMM_FORMAT_STAND_I2S,  // ✅ Исправлено
    .intr_alloc_flags = 0,
    .dma_buf_count = 8,
    .dma_buf_len = 512,
    .use_apll = false,
    .tx_desc_auto_clear = true,
    .fixed_mclk = 0
  };

  i2s_pin_config_t pin_config = {
    .bck_io_num = I2S_BCLK,
    .ws_io_num = I2S_LRC,
    .data_out_num = I2S_DOUT,
    .data_in_num = -1
  };

  i2s_driver_install(I2S_NUM_0, &i2s_config, 0, NULL);
  i2s_set_pin(I2S_NUM_0, &pin_config);
  i2s_zero_dma_buffer(I2S_NUM_0);
}

void playWavFromFS(const char* filename) {
  File f = LittleFS.open(filename, "r");
  if (!f) {
    Serial.printf("File not found: %s\n", filename);
    return;
  }
  uint8_t header[44];
  if (f.read(header, 44) != 44) {
    Serial.println("Invalid WAV header");
    f.close();
    return;
  }
  uint8_t buf[512];
  size_t bytesRead;
  size_t bytesWritten;
  while ((bytesRead = f.read(buf, sizeof(buf))) > 0) {
    i2s_write(I2S_NUM_0, buf, bytesRead, &bytesWritten, portMAX_DELAY);
  }
  f.close();
}

void setup() {
  // === CRITICAL: Даем Serial Monitor время подключиться ===
  delay(2000); // Ждем 2 секунды до открытия Serial Monitor
  
  Serial.begin(115200);
  
  // Добавим несколько попыток, чтобы убедиться, что Serial готов
  uint32_t start_ts = millis();
  while (!Serial && millis() - start_ts < 2000) {
    delay(100); // Ждем максимум 2 секунды, пока Serial не будет готов
  }

  // === DEBUG MARKER ===
  Serial.println("\n--- Durin's Doors - Interactive Booknook Project ---");
  Serial.println("Инициализация...");

  servo_L.attach(SERVO_L_PIN);
  servo_R.attach(SERVO_R_PIN);
  lithophane.begin();
  door_L.begin();
  door_R.begin();
  lithophane.show();
  door_L.show();
  door_R.show();
  servo_L.write(leftClosed);
  servo_R.write(rightClosed);
  delay(1000);
  
  WiFi.softAP(ssid, password);
  Serial.println("[WiFi] AP IP: " + WiFi.softAPIP().toString());
  
  server.on("/", handleRoot);
  server.on("/set_param", HTTP_GET, handleSetParam);
  server.on("/toggle_doors", HTTP_POST, handleToggleDoors);
  server.on("/toggle_litho", HTTP_POST, handleToggleLitho);
  server.on("/toggle_doorlight", HTTP_POST, handleToggleDoorLight);
  server.on("/play_wizard", HTTP_POST, handlePlayWizard);
  server.on("/update", HTTP_POST, []() {
    server.send(200, "text/plain", "Подождите...");
  }, handleUpdate);
  server.onNotFound(handleFileRequest);
  server.begin();
  
  if (!LittleFS.begin()) {
    Serial.println("[FS] LittleFS mount failed!");
  } else {
    Serial.println("[FS] LittleFS mounted");
    loadParamsFromFS();
    Serial.println("[FS] Параметры загружены из FS");
  }
  
  setupI2S();
  battery.begin(3300, 2.0);
  
  Serial.println("[SETUP] Инициализация завершена!");
}

void loop() {
  // КРИТИЧЕСКИ ВАЖНО: для Wi-Fi и прерываний
  yield(); 
  
  unsigned long now = millis();
  static unsigned long lastUpdate = 0;
  
  // Ограничиваем частоту обновления ~100 раз в секунду (не чаще каждые 10 мс)
  if (now - lastUpdate < 10) {
    // Если условие истинно, мы ВЫХОДИМ из loop(), не обновляя lastUpdate
    return;
  }
  // Только если прошло >= 10 мс, обновляем lastUpdate и продолжаем
  lastUpdate = now;

  server.handleClient();
  door_anim_step();
  setDoorLights(door_light_on, door_bright);
  
  // Управление анимацией литофании
  static String last_anim = "";
  static uint8_t last_bright = 0;
  static bool last_on = false;
  static uint8_t last_speed = 0;
  
  if (litho_on) {
    if (litho_anim != last_anim || litho_bright != last_bright || !last_on || litho_speed != last_speed) {
      for (int i = 0; i < NUM_LITHO; i++) lithophane.setPixelColor(i, 0);
      lithophane.show();
      resetLithoAnim();
      last_anim = litho_anim;
      last_bright = litho_bright;
      last_on = true;
      last_speed = litho_speed;
      
      // Отладочный вывод при изменении состояния
      Serial.println("[LITHO] Анимация запущена: " + litho_anim);
    }
    litho_anim_step();
  } else {
    if (last_on) {
      for (int i = 0; i < NUM_LITHO; i++) lithophane.setPixelColor(i, 0);
      lithophane.show();
      last_on = false;
      
      // Отладочный вывод при выключении
      Serial.println("[LITHO] Подсветка выключена");
    }
  }
  
  if (isWizardPlaying && now > wizardPlayEndMs) {
    isWizardPlaying = false;
    Serial.println("[AUDIO] Воспроизведение завершено");
  }
  
  // === Battery monitoring (раз в 5 секунд) ===
  static unsigned long last_batt_print = 0;
  if (now - last_batt_print >= 5000) {
    battery_voltage = battery.voltage();
    battery_percent = battery.level();
    // Выводим только один раз за 5 секунд
    Serial.printf("[BATTERY] 🔋 Voltage: %.2f V, Level: %d%%\n", battery_voltage, battery_percent);
    last_batt_print = now; 
  }
}
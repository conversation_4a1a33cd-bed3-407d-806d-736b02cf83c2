# BookNook LoTR: Edge Impulse KWS Model Tuning Guide

This guide walks you through building and tuning a keyword spotting (KWS) model for your Lord of the Rings BookNook project using Edge Impulse, with your browser as the recording device. It covers setup, data collection, model training, architecture, and deployment for ESP32.

---

## 1. Project Setup & Device Connection
- Sign up or log in at [Edge Impulse Studio](https://studio.edgeimpulse.com/).
- Create a new project (e.g., `BookNook LoTR KWS`).
- In **Devices**, click **Connect a new device** and choose **Use your browser** ("Record with your browser").
- Allow microphone access. Your browser will appear as a device for data collection.

---

## 2. Defining Labels (Keywords)
- Labels are set in the **Data acquisition** tab each time you record a sample.
- Use these labels for BookNook:
  - `mellon` (open doors)
  - `you_shall_not_pass` (Gandalf effect)
  - `hollo` (close doors)
  - `unknown` (other words)
  - `background` (silence/noise)
- Labels are case-sensitive, no spaces (use underscores).

---

## 3. Data Collection & Window Size
- In **Data acquisition**, select your browser device.
- For each label, record at least 20–30 samples (more is better).
- Each sample should fit within your chosen window size (default: 1–2 seconds). For longer commands, increase the window size in your impulse (e.g., 3 seconds for a 3-second command).
- Vary your voice, speed, and environment. For `unknown`, say random words; for `background`, record silence or ambient noise.

---

## 4. Training vs. Testing Set
- Edge Impulse splits your data into **training** (for learning) and **testing** (for evaluation) sets.
- You can view and adjust this split in **Data acquisition**.

| Set           | Used for...         | Model sees during training? | Example in Edge Impulse      |
|---------------|---------------------|----------------------------|------------------------------|
| Training set  | Learning patterns   | Yes                        | Most of your recordings      |
| Testing set   | Evaluating accuracy | No                         | A smaller, separate portion  |

---

## 5. Create Impulse (Model Pipeline)
- Go to **Create impulse**.
- Set the window size to match your longest command (e.g., 1000 ms for 1s, 3000 ms for 3s).
- Add an **MFCC** (audio feature extraction) block.
- Add a **Classification** (Keras) block.
- Save the impulse.

---

## 6. Generate Features & Retrain
- Click the **MFCC** block, review settings, and click **Generate features**. This step is required before training or retraining.
- If you add new data, always regenerate features and retrain the model.

---

## 7. Classifier Block & Architecture
- In the **Classifier** block, you'll see options like:
  - **Number of training cycles:** 100 (default is good)
  - **Learning rate:** 0.005 (default; lower if unstable, higher if too slow)
  - **Data augmentation:** Enable for better generalization
  - **Architecture presets:** Start with "Simple (Dense)" or "TinyML" for ESP32, or use the default CNN preset

**Typical CNN architecture for KWS:**
- **Input layer:** (e.g., 962 features from MFCC)
- **Reshape layer:** (e.g., 13 columns)
- **1D conv/pool layers:** (e.g., 8 filters, then 16 filters, kernel size 3)
- **Dropout layers:** (e.g., rate 0.25) to prevent overfitting
- **Flatten layer**
- **Output layer:** (number of classes/labels, softmax activation)

You can add/remove layers or adjust filters to balance accuracy and model size for ESP32.

---

## 8. Training, Testing, and Tuning
- Click **Start training** in the Classifier block.
- After training, check accuracy on the **testing set**.
- If accuracy is low:
  - Collect more data (especially for misclassified labels)
  - Adjust architecture (filters, layers, dropout)
  - Tune learning rate or window size
  - Enable/adjust data augmentation
- Test live with your browser mic for real-world performance.

---

## 9. Deployment
- Go to **Deployment**.
- Select **ESP32** or **Arduino library** as the target.
- Download and integrate the model into your BookNook firmware.

---

## 10. Best Practices & Tips
- Keep commands short and distinct for best KWS results.
- Always regenerate features and retrain after adding new data.
- Use data augmentation for robustness.
- Regularly test and retrain with new data from your real environment.
- Monitor model size to ensure it fits on your ESP32.

---

**References:**
- [Edge Impulse Docs: Audio classification](https://docs.edgeimpulse.com/docs/edge-impulse-studio/audio-classification)
- [ESP32 with Edge Impulse](https://docs.edgeimpulse.com/docs/edge-impulse-studio/esp32) 
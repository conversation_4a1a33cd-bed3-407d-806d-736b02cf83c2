# Durin's Doors

An interactive booknook project inspired by the magical doors of <PERSON><PERSON> from The Lord of the Rings. This project creates an immersive miniature scene with responsive lighting effects and animations.

## Project Structure

```
DurinsDoors/
├── src/               # Source files
│   └── main.cpp      # Main Arduino sketch
├── include/          # Header files
├── lib/              # Custom libraries
├── platformio.ini    # PlatformIO configuration
└── README.md         # This file
```

## Hardware Requirements

- ESP32 development board
- 3x WS2812B LED strips:
  - 1x 32 LEDs for lithophane
  - 2x 46 LEDs for doors
- Trigger sensor (e.g., motion sensor, button)
- Power supply appropriate for LED strips
- Optional: INMP441 I2S microphone
- Optional: MAX98357A I2S audio DAC
- Optional: Servo motors for mechanical effects

## Pin Configuration

- Trigger Pin: 2
- Lithophane LED Strip: 16
- Right Door LED Strip: 17
- Left Door LED Strip: 18

## Development Setup

### Prerequisites

1. Install [PlatformIO](https://platformio.org/install)
2. Install [Visual Studio Code](https://code.visualstudio.com/)
3. Install the PlatformIO IDE extension in VS Code

### Building and Uploading

1. Clone this repository
2. Open the project in VS Code
3. Wait for PlatformIO to initialize
4. Connect your ESP32 board
5. Click the "Build" button (✓) or press Ctrl+Alt+B
6. Click the "Upload" button (→) or press Ctrl+Alt+U

### Serial Monitor

- Open the serial monitor by clicking the "Serial Monitor" button (🔌)
- Set the baud rate to 115200

## Features

- Interactive door activation/deactivation
- Beautiful LED lighting effects:
  - Diagonal lithophane animation
  - Smooth door lighting with lead pixels
  - Warm white and cyan color schemes
- Trigger cooldown system
- Serial debugging output

## Libraries Used

- Adafruit NeoPixel
- ESP32Servo
- ESP32 Audio

## Future Enhancements

- Add sound effects using I2S audio
- Implement mechanical door movement with servos
- Add multiple trigger types
- Create custom animations
- Add WiFi connectivity for remote control

## License

This project is open source and available under the MIT License. 